package ai.yourouter.common.context;

import ai.yourouter.chat.remote.response.BestKeyResponse;
import ai.yourouter.common.context.usage.ChatUsage;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@Slf4j
public class ChatContext {

    private String requestId;

    private Long retryCount = 0L;

    private ChatUsage chatUsage;

    private ChatUserInfo chatUserInfo;

    private List<BestKeyResponse> availableKeyResponse = new ArrayList<>();

    private ChatModelInfo chatModelInfo = new ChatModelInfo();

    private ChatRequestStatistic chatRequestStatistic = new ChatRequestStatistic();


    public String apiModelName() {
        return chatModelInfo.realModelName();
    }

    public void setKeyInfo(BestKeyResponse availableKeyResponse) {
        this.availableKeyResponse.add(availableKeyResponse);
    }

    public BestKeyResponse getKeyInfo() {
        return this.availableKeyResponse.getLast();
    }


    public static ChatContext create(Map<String, String> requestHeaders, String requestId) {
        ChatContext chatContext = new ChatContext();
        chatContext.getChatRequestStatistic().setRequestHeaders(requestHeaders);
        chatContext.setRequestId(requestId);
        return chatContext;
    }

}
