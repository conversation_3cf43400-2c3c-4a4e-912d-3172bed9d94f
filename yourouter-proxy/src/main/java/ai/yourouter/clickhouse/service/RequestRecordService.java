package ai.yourouter.clickhouse.service;

import ai.yourouter.clickhouse.model.RequestRecord;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 请求记录服务
 * 提供简化的请求记录功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnBean(name = "ckJdbcTemplate")
public class RequestRecordService {

    private final ClickHouseService clickHouseService;

    /**
     * 记录请求完成
     * 
     * @param chatContext 聊天上下文
     * @param responseBody 响应体
     * @param isSuccess 是否成功
     * @param errorMessage 错误信息
     */
    public void recordRequest(ChatContext chatContext, 
                            String responseBody, 
                            boolean isSuccess, 
                            String errorMessage) {
        try {
            RequestRecord requestRecord = buildRequestRecord(chatContext, responseBody, isSuccess, errorMessage);
            clickHouseService.saveRequestRecordAsync(requestRecord)
                    .subscribe(
                            null,
                            error -> log.error("保存请求记录失败 | 请求ID: {} | 错误: {}", 
                                    chatContext.getRequestId(), error.getMessage())
                    );
        } catch (Exception e) {
            log.error("构建请求记录失败 | 请求ID: {} | 错误: {}", 
                    chatContext.getRequestId(), e.getMessage(), e);
        }
    }

    /**
     * 记录成功的请求
     */
    public void recordSuccessRequest(ChatContext chatContext, String responseBody) {
        recordRequest(chatContext, responseBody, true, null);
    }

    /**
     * 记录失败的请求
     */
    public void recordFailedRequest(ChatContext chatContext, String errorMessage) {
        recordRequest(chatContext, null, false, errorMessage);
    }

    /**
     * 构建请求记录
     */
    private RequestRecord buildRequestRecord(ChatContext chatContext, 
                                           String responseBody, 
                                           boolean isSuccess, 
                                           String errorMessage) {
        var requestStatistic = chatContext.getChatRequestStatistic();
        var userInfo = chatContext.getChatUserInfo();
        var modelInfo = chatContext.getChatModelInfo();
        var usage = chatContext.getChatUsage();
        var keyInfo = chatContext.getKeyInfo();
        
        // 使用UTC时间
        LocalDateTime now = LocalDateTime.now(java.time.ZoneOffset.UTC);
        LocalDateTime requestTime = now.minusNanos(requestStatistic.consumerTime() * 1_000_000);
        LocalDateTime responseTime = now;
        
        // 限制响应体大小
        if (responseBody != null && responseBody.length() > 50000) {
            responseBody = responseBody.substring(0, 50000) + "...[truncated]";
        }
        
        return RequestRecord.builder()
                .requestId(chatContext.getRequestId())
                .organizationId(userInfo != null ? userInfo.getCharactersId() : null)
                .requestTime(requestTime)
                .responseTime(responseTime)
                .duration(requestStatistic.consumerTime())
                .requestPath(extractRequestPath(chatContext))
                .httpMethod("POST") // 大部分API都是POST
                .modelName(modelInfo != null ? modelInfo.getModelName() : null)
                .realModelName(modelInfo != null ? modelInfo.realModelName() : null)
                .vendor(requestStatistic.vendorHeader())
                .keyId(keyInfo != null ? keyInfo.getId().toString() : null)
                .keyName(keyInfo != null ? keyInfo.getName() : null)
                .isStream(requestStatistic.onStream())
                .requestBody(JsonUtils.toJSONString(requestStatistic.getRawRequest()))
                .responseBody(responseBody)
                .requestHeaders(JsonUtils.toJSONString(requestStatistic.getRequestHeaders()))
                .responseStatus(isSuccess ? 200 : 500)
                .errorMessage(errorMessage)
                .clientIp(extractClientIp(chatContext))
                .userAgent(requestStatistic.getRequestHeaders().get("user-agent"))
                .cfConnectingIp(requestStatistic.getRequestHeaders().get("cf-connecting-ip"))
                .cfIpCountry(requestStatistic.getRequestHeaders().get("cf-ipcountry"))
                .cfRay(requestStatistic.getRequestHeaders().get("cf-ray"))
                .retryCount(chatContext.getRetryCount())
                .isSuccess(isSuccess)
                .accessToken(userInfo != null ? userInfo.getCharactersAuthKey() : null)
                .createdAt(LocalDateTime.now(java.time.ZoneOffset.UTC))
                .build();
    }

    /**
     * 提取请求路径
     */
    private String extractRequestPath(ChatContext chatContext) {
        var requestHeaders = chatContext.getChatRequestStatistic().getRequestHeaders();
        String path = requestHeaders.get("request-path");
        if (path != null) {
            return path;
        }
        
        // 根据模型和供应商推断路径
        var modelInfo = chatContext.getChatModelInfo();
        if (modelInfo != null) {
            String vendor = chatContext.getChatRequestStatistic().vendorHeader();
            if ("Anthropic".equals(vendor)) {
                return "/v1/messages";
            } else if ("Google".equals(vendor)) {
                return "/v1/projects/cognition/locations/us/publishers/google/models/" + modelInfo.getModelName() + ":generateContent";
            } else {
                return "/v1/chat/completions";
            }
        }
        
        return "/unknown";
    }

    /**
     * 提取客户端IP
     */
    private String extractClientIp(ChatContext chatContext) {
        var requestHeaders = chatContext.getChatRequestStatistic().getRequestHeaders();
        
        String xForwardedFor = requestHeaders.get("x-forwarded-for");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = requestHeaders.get("x-real-ip");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        String cfConnectingIp = requestHeaders.get("cf-connecting-ip");
        if (cfConnectingIp != null && !cfConnectingIp.isEmpty()) {
            return cfConnectingIp;
        }
        
        return "unknown";
    }
}
