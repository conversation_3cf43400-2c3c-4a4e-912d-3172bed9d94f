package ai.yourouter.chat.remote;

import ai.yourouter.chat.remote.request.KeyUsageResultRequest;
import ai.yourouter.chat.remote.response.BestKeyResponse;
import ai.yourouter.chat.remote.response.PlatformResult;
import ai.yourouter.common.exception.CognitionWebException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class KmgRemoteService {

    @Qualifier("httpWebClient")
    private final WebClient httpWebClient;

    @Value("${kmg.service.base-url:http://127.0.0.1:8070}")
    private String kmgBaseUrl;


    /**
     * 发送禁用事件
     *
     * @param keyId 密钥ID
     * @return 响应结果
     */
    public Mono<String> disable(String keyId) {
        String url = kmgBaseUrl + "/api/google-llm-keys/events/disable/" + keyId;
        log.info("发送禁用事件 | 密钥ID: {} | URL: {}", keyId, url);

        return httpWebClient.post()
                .uri(url)
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> log.info("禁用事件发送成功 | 密钥ID: {} | 响应: {}", keyId, response))
                .doOnError(error -> log.error("禁用事件发送失败 | 密钥ID: {} | 错误: {}", keyId, error.getMessage()));
    }


    public Mono<String> result(String keyId, String modelName, boolean onSuccess,boolean needDeleted) {
        String url = kmgBaseUrl + "/api/v1/key-stats//model/" + modelName + "/key/" + keyId + "/result";
        log.info("发送成功事件 | 密钥ID: {} | URL: {}", keyId, url);

        return httpWebClient.post()
                .uri(url)
                .bodyValue(new KeyUsageResultRequest(onSuccess,needDeleted))
                .retrieve()
                .bodyToMono(String.class)
                .doOnSuccess(response -> log.info("调用密钥结果事件发送成功 | 密钥ID: {} | 响应: {}", keyId, response))
                .doOnError(error -> log.error("调用密钥结果事件发送失败 | 密钥ID: {} | 错误: {}", keyId, error.getMessage()));
    }


    /**
     * 获取可用密钥
     *
     * @return 响应结果
     */
    public Mono<BestKeyResponse> getAvailable(String modelName, String vendorNames) {
        String url = kmgBaseUrl + "/api/v1/key-stats/best-key/" + modelName;
        if (StringUtils.hasLength(vendorNames)) {
            url = url + "?channels=" + vendorNames;
        }

        log.info("获取可用密钥 | URL: {}", url);

        // 使用 ParameterizedTypeReference 来正确处理泛型反序列化
        return httpWebClient.get()
                .uri(url)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<PlatformResult<BestKeyResponse>>() {})
                .doOnNext(platformResult -> {
                    // 记录完整的平台响应，便于调试
                    log.debug("平台响应详情 | requestId: {} | code: {} | desc: {} | success: {} | data: {}",
                            platformResult.getRequestId(),
                            platformResult.getCode(),
                            platformResult.getDesc(),
                            platformResult.getSuccess(),
                            platformResult.getData());
                })
                .flatMap(platformResult -> {
                    // 检查平台响应是否成功
                    if (platformResult.getSuccess() == null || !platformResult.getSuccess()) {
                        String errorMsg = String.format("平台返回失败响应 | code: %s | desc: %s",
                                platformResult.getCode(), platformResult.getDesc());
                        log.warn(errorMsg);

                        // 根据是否指定vendor生成不同的英文错误消息
                        String userErrorMsg;
                        if (StringUtils.hasLength(vendorNames)) {
                            userErrorMsg = "No resources available for vendor: " + vendorNames;
                        } else {
                            userErrorMsg = "No resources available";
                        }

                        // 使用 CognitionWebException，不触发重试机制
                        return Mono.error(new CognitionWebException(userErrorMsg));
                    }

                    // 检查 data 字段是否为 null
                    BestKeyResponse data = platformResult.getData();
                    if (data == null) {
                        String errorMsg = String.format("平台返回的密钥数据为空 | code: %s | desc: %s | requestId: %s",
                                platformResult.getCode(), platformResult.getDesc(), platformResult.getRequestId());
                        log.warn(errorMsg);

                        // 根据是否指定vendor生成不同的英文错误消息
                        String userErrorMsg;
                        if (StringUtils.hasLength(vendorNames)) {
                            userErrorMsg = "No resources available for vendor: " + vendorNames;
                        } else {
                            userErrorMsg = "No resources available";
                        }

                        // 使用 CognitionWebException，不触发重试机制
                        return Mono.error(new CognitionWebException(userErrorMsg));
                    }

                    return Mono.just(data);
                })
                .doOnSuccess(response -> log.info("获取可用密钥成功 | 响应: {}", response))
                .doOnError(error -> log.error("获取可用密钥失败 | 错误: {}", error.getMessage()));
    }
}

