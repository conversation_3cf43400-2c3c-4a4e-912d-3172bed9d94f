package ai.yourouter.chat.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Optional;

/**
 * 请求验证工具类
 */
@Slf4j
public class RequestValidationUtils {

    private RequestValidationUtils() {
        // 工具类禁止实例化
    }

    /**
     * 验证聊天完成请求参数
     */
    public static boolean isValidChatRequest(LinkedHashMap<String, Object> req) {
        if (req == null || req.isEmpty()) {
            log.warn("请求参数为空");
            return false;
        }
        
        // 验证model参数
        Object model = req.get("model");
        if (model == null || !StringUtils.hasText(model.toString())) {
            log.warn("model参数缺失或为空");
            return false;
        }
        
        // 验证messages参数（如果存在）
        Object messages = req.get("messages");
        if (messages != null && !(messages instanceof java.util.List)) {
            log.warn("messages参数格式错误");
            return false;
        }
        
        return true;
    }

    /**
     * 解析stream参数
     */
    public static boolean parseStreamParameter(LinkedHashMap<String, Object> req) {
        Object streamObj = req.getOrDefault("stream", false);
        if (streamObj instanceof Boolean) {
            return (Boolean) streamObj;
        }
        if (streamObj instanceof String) {
            return Boolean.parseBoolean((String) streamObj);
        }
        return false;
    }

    /**
     * 创建请求副本，避免修改原始请求
     */
    public static HashMap<String, Object> createRequestCopy(LinkedHashMap<String, Object> original) {
        return new HashMap<>(original);
    }

    /**
     * 确保请求包含必要的默认值
     */
    public static void ensureDefaultValues(HashMap<String, Object> req, boolean isStream) {
        req.put("stream", isStream);
        
        // 为流式请求添加usage选项
        if (isStream ) {
            if (req.containsKey("stream_options")){
                var streamOptions = Optional.ofNullable(req.get("stream_options")).orElse(new HashMap<>());
                ((HashMap<String, Object>) streamOptions).put("include_usage", true);
                req.put("stream_options", streamOptions);
            }else{
                req.put("stream_options", new HashMap<String, Object>() {{
                    put("include_usage", true);
                }});
            }
        }
    }
}
