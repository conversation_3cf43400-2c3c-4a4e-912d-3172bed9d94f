{"time":"2025-07-31T22:50:55.948174+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":113},"msg":"Getting live provider data"}
{"time":"2025-07-31T22:50:56.907184+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-07-31T22:50:57.38684+08:00","level":"INFO","msg":"OK   20250424200609_initial.sql (605.42µs)"}
{"time":"2025-07-31T22:50:57.38708+08:00","level":"INFO","msg":"OK   20250515105448_add_summary_message_id.sql (219.13µs)"}
{"time":"2025-07-31T22:50:57.387241+08:00","level":"INFO","msg":"OK   20250624000000_add_created_at_indexes.sql (151.17µs)"}
{"time":"2025-07-31T22:50:57.387432+08:00","level":"INFO","msg":"OK   20250627000000_add_provider_to_messages.sql (183.67µs)"}
{"time":"2025-07-31T22:50:57.387437+08:00","level":"INFO","msg":"goose: successfully migrated database to version: 20250627000000"}
{"time":"2025-07-31T22:50:57.38753+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-07-31T22:50:57.400434+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-07-31T22:50:57.400587+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-07-31T22:50:57.410438+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-07-31T22:50:57.410487+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-07-31T22:51:02.151159+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-07-31T22:51:02.151252+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-07-31T22:51:02.167301+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-07-31T22:51:02.167362+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-07-31T22:51:05.0663+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":1,"max_retries":8}
{"time":"2025-07-31T22:51:05.0663+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":1,"max_retries":8}
{"time":"2025-07-31T22:51:06.04761+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":2,"max_retries":8}
{"time":"2025-07-31T22:51:06.527677+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":2,"max_retries":8}
{"time":"2025-07-31T22:51:06.697405+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":3,"max_retries":8}
{"time":"2025-07-31T22:51:08.072717+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":4,"max_retries":8}
{"time":"2025-07-31T22:51:08.072717+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":3,"max_retries":8}
{"time":"2025-07-31T22:51:08.757673+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":5,"max_retries":8}
{"time":"2025-07-31T22:51:09.280999+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":4,"max_retries":8}
{"time":"2025-07-31T22:51:09.332102+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":6,"max_retries":8}
{"time":"2025-07-31T22:51:09.836898+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":7,"max_retries":8}
{"time":"2025-07-31T22:51:10.162606+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":5,"max_retries":8}
{"time":"2025-07-31T22:51:10.386136+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":8,"max_retries":8}
{"time":"2025-07-31T22:51:10.858991+08:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processGeneration.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":369},"msg":"failed to generate title","error":"maximum retry attempts reached for rate limit: 8 retries"}
{"time":"2025-07-31T22:51:11.070971+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":6,"max_retries":8}
{"time":"2025-07-31T22:51:11.844636+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":7,"max_retries":8}
{"time":"2025-07-31T22:51:12.697781+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":8,"max_retries":8}
{"time":"2025-07-31T22:51:13.480515+08:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).Run.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":343},"msg":"failed to process events: maximum retry attempts reached for rate limit: 8 retries"}
{"time":"2025-07-31T22:51:45.326163+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:51:46.397443+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":1,"max_retries":8}
{"time":"2025-07-31T22:51:46.397511+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:51:47.172018+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":2,"max_retries":8}
{"time":"2025-07-31T22:51:47.172101+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:51:47.92672+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":3,"max_retries":8}
{"time":"2025-07-31T22:51:47.926789+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:51:48.771476+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":4,"max_retries":8}
{"time":"2025-07-31T22:51:48.771536+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:51:49.808568+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":5,"max_retries":8}
{"time":"2025-07-31T22:51:49.808645+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:51:51.248895+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":6,"max_retries":8}
{"time":"2025-07-31T22:51:51.24897+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:51:52.591672+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":7,"max_retries":8}
{"time":"2025-07-31T22:51:52.591766+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:51:53.632477+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":8,"max_retries":8}
{"time":"2025-07-31T22:51:53.632559+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:51:54.379558+08:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).Run.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":343},"msg":"failed to process events: maximum retry attempts reached for rate limit: 8 retries"}
{"time":"2025-07-31T22:52:16.156098+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-07-31T22:52:16.157235+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-07-31T22:52:16.624138+08:00","level":"INFO","msg":"goose: no migrations to run. current version: 20250627000000"}
{"time":"2025-07-31T22:52:16.624184+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-07-31T22:52:16.63585+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-07-31T22:52:16.635929+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-07-31T22:52:16.645659+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-07-31T22:52:16.64571+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-07-31T22:52:17.217761+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-07-31T22:52:21.24015+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":1,"max_retries":8}
{"time":"2025-07-31T22:52:21.241027+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":1,"max_retries":8}
{"time":"2025-07-31T22:52:21.820818+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":2,"max_retries":8}
{"time":"2025-07-31T22:52:22.156547+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":2,"max_retries":8}
{"time":"2025-07-31T22:52:22.156547+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":3,"max_retries":8}
{"time":"2025-07-31T22:52:22.703335+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":4,"max_retries":8}
{"time":"2025-07-31T22:52:22.98127+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":3,"max_retries":8}
{"time":"2025-07-31T22:52:23.17209+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":5,"max_retries":8}
{"time":"2025-07-31T22:52:23.521208+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":6,"max_retries":8}
{"time":"2025-07-31T22:52:23.799851+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":4,"max_retries":8}
{"time":"2025-07-31T22:52:24.029655+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":7,"max_retries":8}
{"time":"2025-07-31T22:52:24.407821+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":8,"max_retries":8}
{"time":"2025-07-31T22:52:24.578617+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":5,"max_retries":8}
{"time":"2025-07-31T22:52:24.795534+08:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processGeneration.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":369},"msg":"failed to generate title","error":"maximum retry attempts reached for rate limit: 8 retries"}
{"time":"2025-07-31T22:52:25.416299+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":6,"max_retries":8}
{"time":"2025-07-31T22:52:26.440978+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":7,"max_retries":8}
{"time":"2025-07-31T22:52:27.180652+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":8,"max_retries":8}
{"time":"2025-07-31T22:52:27.92013+08:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).Run.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":343},"msg":"failed to process events: maximum retry attempts reached for rate limit: 8 retries"}
{"time":"2025-07-31T22:52:36.607595+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:52:39.985424+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":1,"max_retries":8}
{"time":"2025-07-31T22:52:39.985515+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:52:40.807111+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":2,"max_retries":8}
{"time":"2025-07-31T22:52:40.807258+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:52:41.388692+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":3,"max_retries":8}
{"time":"2025-07-31T22:52:41.388804+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:52:42.047576+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":4,"max_retries":8}
{"time":"2025-07-31T22:52:42.047721+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:52:42.666941+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":5,"max_retries":8}
{"time":"2025-07-31T22:52:42.66709+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:52:43.291661+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":6,"max_retries":8}
{"time":"2025-07-31T22:52:43.29183+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:52:43.97535+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":7,"max_retries":8}
{"time":"2025-07-31T22:52:43.975488+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:52:44.833455+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":442},"msg":"Retrying due to rate limit","attempt":8,"max_retries":8}
{"time":"2025-07-31T22:52:44.833528+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":134},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-07-31T22:52:45.526895+08:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).Run.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":343},"msg":"failed to process events: maximum retry attempts reached for rate limit: 8 retries"}
{"time":"2025-07-31T22:53:03.065804+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-07-31T22:53:03.067243+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-07-31T22:53:03.53421+08:00","level":"INFO","msg":"goose: no migrations to run. current version: 20250627000000"}
{"time":"2025-07-31T22:53:03.534258+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-07-31T22:53:03.544839+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-07-31T22:53:03.544926+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-07-31T22:53:03.55488+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-07-31T22:53:03.554948+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-07-31T22:53:04.275548+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
